{"name": "PostInspirationPage", "description": "Gundam KRN", "version": "0.0.0", "author": "zhangxu33 <<EMAIL>>", "private": true, "license": "ISC", "protected": true, "repository": {}, "main": "src/index.tsx", "scripts": {"start": "krn start-webpack", "start:metro": "krn start", "build": "krn build --use-webpack", "debug": "krn start --debug", "lint": "eslint --cache --fix --ext .js,.cjs,.mjs,.jsx,.ts,.tsx .", "lint:only": "eslint --ext .js,.cjs,.mjs,.jsx,.ts,.tsx .", "gen:weblogger": "mfe-plus-weblogger-gen", "postinstall": "krn-webpack patch", "analysis": "krn bundle-visualizer", "prepare": "husky", "api:remote": "openapi-generator-typescript --update && openapi-generator-typescript --generate", "api:local": "openapi-generator-typescript --generate"}, "engines": {"node": ">=18.12", "pnpm": "^8"}, "dependencies": {"@kds/bridge-lite": "^1.2.4", "@kds/image": "^1.15.9", "@kds/player": "^2.8.13", "@kds/react-native-gesture-handler": "^1.7.26", "@kds/react-native-safe-area-context": "^3.2.2", "@kds/react-native-svg": "^12.2.6", "@kds/refresh-list": "^4.0.9", "@kds/warmup-image": "^0.5.3", "@kid-ui/krn": "^0.8.10", "@ks-radar/radar": "^1.3.1", "@ks/weblogger": "^3.10.45", "@lux/krn-error-boundary": "^1.3.1", "@lux/krn-waterfall-list-view": "^0.12.0", "@mfe/babel-plugin-macros": "^4.1.0", "@mfe/plus": "^0.1.1", "@mfe/plus-style": "^0.0.11", "@mfe/plus-utils": "^0.0.9", "@weblogger/ts-adapter": "^1.0.7", "react": "16.11.0", "react-native": "0.62.2", "react-native-svg": "^12.5.1"}, "devDependencies": {"@babel/core": "^7", "@babel/helper-module-imports": "^7", "@babel/runtime": "^7", "@babel/types": "^7", "@gundam/api-plus-client": "^1.0.4", "@gundam/gundam-plugin-api-generator": "^1.2.3", "@gundam/gundam-plugin-gitlab": "^1.3.15", "@gundam/********************kds": "^1.0.3", "@gundam/gundam-plugin-kdev": "^1.7.2", "@gundam/gundam-plugin-keep": "^1.2.10", "@gundam/gundam-plugin-kfc": "^2.2.5", "@gundam/gundam-plugin-krn-template": "^1.4.10", "@gundam/gundam-plugin-radar": "^1.3.5", "@gundam/krn-plugin-plus-style": "^0.1.1", "@gundam/krn-plugin-runner": "^1.3.2", "@gundam/webpack": "latest", "@kds/bridge-lite-types": "^1.0.691", "@kds/devtools-lite": "^0.1.26", "@krn/cli": "^1.14.0", "@krn/inline-require-webpack-plugin": "^0.5.0", "@krn/module-require-trace-webpack-plugin": "^0.2.0", "@krn/webpack-tools": "^0.6.9", "@pex/openapi-generator-typescript": "0.1.0-alpha.28", "@react-native-community/eslint-plugin": "^1.3.0", "@tsconfig/react-native": "^3.0.5", "@types/kdsbridge": "^0.1.5", "@types/react": "16.9.56", "@types/react-native": "^0.62.2", "@typescript-eslint/eslint-plugin": "^8", "@typescript-eslint/parser": "^8", "babel-plugin-module-resolver": "^5.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "circular-dependency-plugin": "^5.2.2", "eslint": "<9", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "4.2.3", "eslint-plugin-import": "^2.32.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "metro-react-native-babel-preset": "0.59.0", "prettier": "^3.6.2", "typescript": "<5.6.0"}, "resolutions": {"@react-native-community/cli": "~4.13.1", "@react-native-community/cli-tools": "~4.13.0", "@types/react": "16.9.56", "metro-react-native-babel-preset": "0.59.0", "semver": "^7"}, "packageManager": "pnpm@8.15.9"}