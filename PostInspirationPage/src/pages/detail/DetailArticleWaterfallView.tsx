import React, { type VFC } from 'react';
import { StyleSheet, View } from 'react-native';
import DetailArticleCard from './DetailArticleCard';
import { type ArticleBean, type DetailModel } from './DetailModels';
import { DetailUtils } from './DetailUtils';

interface DetailArticleWaterfallViewProps {
  /** 详情数据模型 */
  model: DetailModel;
  /** 文章点击事件处理函数 */
  onArticlePress?: (article: ArticleBean) => void;
}

const styles = StyleSheet.create({
  // 左侧文章卡片容器
  articleCardItemLeft: {
    flex: 1,
    marginRight: 3,
    paddingHorizontal: 0,
  },
  // 右侧文章卡片容器
  articleCardItemRight: {
    flex: 1,
    marginLeft: 3,
    paddingHorizontal: 0,
  },
  // 文章卡片行容器
  articleCardRow: {
    flexDirection: 'row',
    marginBottom: 0,
  },
  // 文章卡片网格容器
  articleCardsGrid: {
    paddingVertical: 6,
  },
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DetailArticleWaterfallView: VFC<DetailArticleWaterfallViewProps> = ({ model, onArticlePress }) => {
  // 在组件内部创建文章数据
  const articles: ArticleBean[] = DetailUtils.createArticles();

  // 将文章按行分组，每行两个
  const renderArticleRows = () => {
    const rows = [];
    for (let i = 0; i < articles.length; i += 2) {
      const leftArticle = articles[i];
      const rightArticle = articles[i + 1];

      rows.push(
        <View key={`row-${i}`} style={styles.articleCardRow}>
          {/* 左侧文章 */}
          <View style={styles.articleCardItemLeft}>
            <DetailArticleCard article={leftArticle} onPress={onArticlePress} />
          </View>
          {/* 右侧文章（如果存在） */}
          {rightArticle && (
            <View style={styles.articleCardItemRight}>
              <DetailArticleCard article={rightArticle} onPress={onArticlePress} />
            </View>
          )}
          {/* 如果右侧没有文章，添加占位符保持布局 */}
          {!rightArticle && <View style={styles.articleCardItemRight} />}
        </View>,
      );
    }
    return rows;
  };

  return <View style={styles.articleCardsGrid}>{renderArticleRows()}</View>;
};

export default DetailArticleWaterfallView;
