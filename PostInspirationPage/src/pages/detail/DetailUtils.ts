import {
  type ArticleBean,
  type DetailModel,
  type ImageSource,
  type VideoSource,
} from './DetailModels';

/**
 * DetailUtils - 详情页工具类
 * 提供详情页相关的工具方法
 */
export class DetailUtils {
  /**
   * 演示图片源
   */
  private static readonly demoImageSource: ImageSource = {
    uri: 'http://tx2.a.yximgs.com/upic/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-0-xe9lwte343-7063eebe5d31cb0e&type=hot&di=676bd8e6&bp=10332',
    uris: [
      {
        cdn: 'tx2.a.yximgs.com',
        url: 'http://tx2.a.yximgs.com/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-0-xe9lwte343-7063eebe5d31cb0e&type=hot&di=676bd8e6&bp=10332',
      },
      {
        cdn: 'ali2.a.yximgs.com',
        url: 'http://ali2.a.yximgs.com/upic/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-1-sspx4pcxcb-90965aadd9ea9e25&type=hot&di=676bd8e6&bp=10332',
      },
    ],
  };

  /**
   * 演示视频源
   */
  private static readonly demoVideoSource: VideoSource = {
    uri: 'https://ali2.a.yximgs.com/udata/pkg/KS-COVERTAG/feedCardTemplate/keling_video.mp4',
    shouldCache: true,
    isNetwork: true,
    alphaType: 0,
    type: 'mp4',
    isAsset: false,
  };

  /**
   * 解析并创建DetailModel实例
   * @returns DetailModel实例
   */
  static parseAndCreateDetailModel(): DetailModel {
    const usageCountDesc = DetailUtils.formatUsageCount(10000);

    return {
      materialType: 'template',
      materialId: 'magic_001',
      materialName: 'EMO 小倭瓜',
      coverSource: DetailUtils.demoImageSource,
      coverWidth: 414,
      coverHeight: 300,
      videoSource: DetailUtils.demoVideoSource,
      videoWidth: 0,
      videoHeight: 0,
      usageDescription: usageCountDesc,
      userName: '小甜甜的日常',
      userAvatarSource: DetailUtils.demoImageSource,
      scheme: 'app://magic/magic_001',
      isFavorite: false,
      enterFrom: 'home',
    };
  }

  /**
   * 格式化使用次数显示
   * @param usageCount 使用次数
   * @returns 格式化后的使用次数描述
   */
  static formatUsageCount(usageCount: number): string {
    return usageCount >= 10000
      ? `${(usageCount / 10000).toFixed(1)}万次使用`
      : `${usageCount}次使用`;
  }

  /**
   * 创建文章数据列表
   * @returns ArticleBean数组
   */
  static createArticles(): ArticleBean[] {
    const demoArticle: ArticleBean = {
      id: '1',
      type: 'video',
      coverSource: DetailUtils.demoImageSource,
      coverWidth: 200,
      coverHeight: 150,
      videoDuration: 0,
      content: '普吉岛⛴  永远鲜花的生命力呀呀呀呀~',
      userName: '视频达人',
      userAvatarSource: DetailUtils.demoImageSource,
      likeCount: 1280000,
      postTime: '5小时前',
      scheme: '',
    };

    return [demoArticle, demoArticle, demoArticle, demoArticle];
  }
}
