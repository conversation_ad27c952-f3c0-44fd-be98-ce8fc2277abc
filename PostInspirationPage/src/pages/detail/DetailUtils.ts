import { NativeModules, Platform, StatusBar } from 'react-native';
import {
  type ArticleBean,
  type DetailModel,
  type ImageSource,
  type VideoSource,
} from './DetailModels';

/**
 * DetailUtils - 详情页工具类
 * 提供详情页相关的工具方法
 */
export class DetailUtils {
  /**
   * 演示图片源
   */
  private static readonly demoImageSource: ImageSource = {
    uri: 'http://tx2.a.yximgs.com/upic/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-0-xe9lwte343-7063eebe5d31cb0e&type=hot&di=676bd8e6&bp=10332',
    uris: [
      {
        cdn: 'tx2.a.yximgs.com',
        url: 'http://tx2.a.yximgs.com/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-0-xe9lwte343-7063eebe5d31cb0e&type=hot&di=676bd8e6&bp=10332',
      },
      {
        cdn: 'ali2.a.yximgs.com',
        url: 'http://ali2.a.yximgs.com/upic/2020/10/16/18/BMjAyMDEwMTYxODQ3MDNfMTg4ODg4ODgwXzM3NzczMTI1OTUwXzFfMw==_low_B93c4659d7b0cc4c96e5f1792399829e4.webp?tag=1-1603070599-bs-1-sspx4pcxcb-90965aadd9ea9e25&type=hot&di=676bd8e6&bp=10332',
      },
    ],
  };

  /**
   * 演示视频源
   */
  private static readonly demoVideoSource: VideoSource = {
    uri: 'https://ali2.a.yximgs.com/udata/pkg/KS-COVERTAG/feedCardTemplate/keling_video.mp4',
    shouldCache: true,
    isNetwork: true,
    alphaType: 0,
    type: 'mp4',
    isAsset: false,
  };

  /**
   * 解析并创建DetailModel实例
   * @returns DetailModel实例
   */
  static parseAndCreateDetailModel(): DetailModel {
    const usageCountDesc = DetailUtils.formatUsageCount(10000);

    return {
      materialType: 'topic',
      materialId: 'magic_001',
      materialName: 'EMO 小倭瓜',
      coverSource: DetailUtils.demoImageSource,
      coverWidth: 414,
      coverHeight: 300,
      videoSource: DetailUtils.demoVideoSource,
      videoWidth: 0,
      videoHeight: 0,
      usageDescription: usageCountDesc,
      userName: '小甜甜的日常',
      userAvatarSource: DetailUtils.demoImageSource,
      scheme: 'app://magic/magic_001',
      isFavorite: false,
      enterFrom: 'home',
    };
  }

  /**
   * 格式化使用次数显示
   * @param usageCount 使用次数
   * @returns 格式化后的使用次数描述
   */
  static formatUsageCount(usageCount: number): string {
    return usageCount >= 10000
      ? `${(usageCount / 10000).toFixed(1)}万次使用`
      : `${usageCount}次使用`;
  }

  /**
   * 创建文章数据列表
   * @returns ArticleBean数组
   */
  static createArticles(n = 4): ArticleBean[] {
    // 定义可选的宽高比例组合
    const aspectRatios = [
      { width: 1, height: 1 },
      { width: 3, height: 4 },
      { width: 4, height: 3 },
      { width: 16, height: 9 },
      { width: 9, height: 16 },
    ];

    // 创建多个文章，每个都有不同的随机宽高比
    const articles: ArticleBean[] = [];

    for (let i = 0; i < n; i++) {
      // 为每个文章随机选择一个宽高比例
      const randomRatio = aspectRatios[Math.floor(Math.random() * aspectRatios.length)];

      const article: ArticleBean = {
        id: `${i + 1}`,
        type: 'video',
        coverSource: DetailUtils.demoImageSource,
        coverWidth: randomRatio.width,
        coverHeight: randomRatio.height,
        videoDuration: 0,
        content: '普吉岛⛴  永远鲜花的生命力呀呀呀呀~',
        userName: '视频达人',
        userAvatarSource: DetailUtils.demoImageSource,
        likeCount: 1280000,
        postTime: '5小时前',
        scheme: '',
      };

      articles.push(article);
    }

    return articles;
  }

  static setStatusBar = () => {
    // 设置状态栏样式
    StatusBar.setBarStyle('dark-content', true);

    if (Platform.OS === 'android') {
      // Android: 设置状态栏背景色为黑色
      StatusBar.setBackgroundColor('#19191E', true);

      // Android: 设置底部导航栏为黑色 - 使用强制设置确保生效
      DetailUtils.forceSetAndroidNavigationBarColor('#19191E');
    } else if (Platform.OS === 'ios') {
      // iOS: 状态栏样式通过StatusBar组件和Info.plist配置
      // iOS没有底部导航栏，但可以设置状态栏为黑色内容
      StatusBar.setBarStyle('dark-content', true);

      // iOS: 如果需要隐藏状态栏，可以使用：
      // StatusBar.setHidden(false, 'fade');

      // iOS: 状态栏背景色需要通过原生代码或者在根视图设置背景色
      console.log('iOS状态栏设置为light-content');
    }

    return () => {
      StatusBar.setBarStyle('default', true);
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('#19191E', true);
      } else if (Platform.OS === 'ios') {
        StatusBar.setBarStyle('default', true);
      }
    };
  };

  // 检测是否为 Web 平台
  isWeb = Platform.OS === 'web';

  // Android 导航栏颜色设置函数 - 使用多种方法确保生效
  static setAndroidNavigationBarColor = (color: string) => {
    if (Platform.OS !== 'android') return;

    // 方法1: 尝试使用 NavigationBarColor 原生模块
    try {
      if (NativeModules.NavigationBarColor) {
        NativeModules.NavigationBarColor.setNavigationBarColor(color);
        console.log('✅ 方法1: NavigationBarColor 模块设置成功');
        return;
      }
    } catch (error) {
      console.log('❌ 方法1: NavigationBarColor 模块失败:', error);
    }

    // 方法2: 尝试使用 StatusBarManager 设置导航栏
    try {
      if (NativeModules.StatusBarManager && NativeModules.StatusBarManager.setNavigationBarColor) {
        NativeModules.StatusBarManager.setNavigationBarColor(color, false);
        console.log('✅ 方法2: StatusBarManager 设置成功');
        return;
      }
    } catch (error) {
      console.log('❌ 方法2: StatusBarManager 失败:', error);
    }

    // 方法3: 尝试使用通用的原生模块调用
    try {
      if (NativeModules.RNNavigationBarColor) {
        NativeModules.RNNavigationBarColor.setNavigationBarColor(color);
        console.log('✅ 方法3: RNNavigationBarColor 设置成功');
        return;
      }
    } catch (error) {
      console.log('❌ 方法3: RNNavigationBarColor 失败:', error);
    }

    console.log('⚠️ 所有导航栏设置方法都失败，可能需要原生模块支持');
  };

  // 强制设置 Android 导航栏颜色 - 使用延迟和重试机制
  static forceSetAndroidNavigationBarColor = (color: string) => {
    if (Platform.OS !== 'android') return;

    // 立即尝试设置
    DetailUtils.setAndroidNavigationBarColor(color);

    // 延迟100ms后重试
    setTimeout(() => {
      DetailUtils.setAndroidNavigationBarColor(color);
    }, 100);

    // 延迟500ms后再次重试
    setTimeout(() => {
      DetailUtils.setAndroidNavigationBarColor(color);
    }, 500);

    // 延迟1000ms后最后一次重试
    setTimeout(() => {
      DetailUtils.setAndroidNavigationBarColor(color);
    }, 1000);
  };

}
