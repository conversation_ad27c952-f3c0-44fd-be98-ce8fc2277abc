import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Animated, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import KwaiImage from '@kds/image';
import { type DetailModel } from './DetailModels';

interface DetailTitleBarViewProps {
  model: DetailModel;
  onBackPress?: () => void;
  onHomePress?: () => void;
}

// 对外暴露的方法接口
export interface DetailTitleBarViewRef {
  showTitle: () => void;
  hideTitle: () => void;
}

const styles = StyleSheet.create({
  backButton: {
    alignItems: 'center',
    height: 40,
    justifyContent: 'center',
    marginLeft: 11,
    width: 40,
  },

  backButtonIcon: {
    height: 40,
    width: 40,
  },

  homeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: 11,
  },

  homeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
  },

  titleBar: {
    alignItems: 'center',
    backgroundColor: '#19191E',
    flexDirection: 'row',
    height: 44,
    width: '100%',
  },

  titleText: {
    color: '#E6E6E6',
    fontSize: 16,
    fontWeight: '500',
    left: 0,
    marginHorizontal: 60,
    position: 'absolute',
    right: 0,
    textAlign: 'center',
  },
});

const DetailTitleBarView = forwardRef<DetailTitleBarViewRef, DetailTitleBarViewProps>(
  ({ model, onBackPress = () => {}, onHomePress = () => {} }, ref) => {
    // 创建动画值，初始值为0（隐藏）
    const titleOpacity = useRef(new Animated.Value(0)).current;

    // 对外暴露的方法
    useImperativeHandle(ref, () => ({
      showTitle() {
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();
      },
      hideTitle() {
        Animated.timing(titleOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start();
      },
    }));

    return (
      <View style={styles.titleBar}>
        <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
          <KwaiImage
            source={require('../../imgs/icon_back.png')}
            style={styles.backButtonIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
        {/* 使用 Animated.Text 并应用透明度动画 */}
        <Animated.Text
          style={[styles.titleText, { opacity: titleOpacity }]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {model.materialName}
        </Animated.Text>
        <TouchableOpacity style={styles.homeButton} onPress={onHomePress}>
          <Text style={styles.homeButtonText}>进入灵感库</Text>
        </TouchableOpacity>
      </View>
    );
  },
);

// 设置组件显示名称
DetailTitleBarView.displayName = 'DetailTitleBarView';

export default DetailTitleBarView;
