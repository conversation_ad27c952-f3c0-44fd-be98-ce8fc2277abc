import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import KwaiImage from '@kds/image';
import { type DetailModel } from './DetailModels';

interface DetailTitleBarViewProps {
  model: DetailModel;
  onBackPress?: () => void;
  onHomePress?: () => void;
}

const styles = StyleSheet.create({
  backButton: {
    alignItems: 'center',
    height: 40,
    justifyContent: 'center',
    marginLeft: 11,
    width: 40,
  },

  backButtonIcon: {
    height: 40,
    width: 40,
  },

  homeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: 11,
  },

  homeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '400',
  },

  titleBar: {
    alignItems: 'center',
    backgroundColor: '#19191E',
    flexDirection: 'row',
    height: 44,
    width: '100%',
  },

  titleText: {
    color: '#E6E6E6',
    fontSize: 16,
    fontWeight: '500',
    left: 0,
    marginHorizontal: 60,
    position: 'absolute',
    right: 0,
    textAlign: 'center',
  },
});

const DetailTitleBarView: React.FC<DetailTitleBarViewProps> = ({
  model,
  onBackPress = () => {},
  onHomePress = () => {},
}) => {
  return (
    <View style={styles.titleBar}>
      <TouchableOpacity style={styles.backButton} onPress={onBackPress}>
        <KwaiImage
          source={require('../../imgs/icon_back.png')}
          style={styles.backButtonIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>
      <Text style={styles.titleText} numberOfLines={1} ellipsizeMode="tail">
        {model.materialName}
      </Text>
      <TouchableOpacity style={styles.homeButton} onPress={onHomePress}>
        <Text style={styles.homeButtonText}>进入灵感库</Text>
      </TouchableOpacity>
    </View>
  );
};

export default DetailTitleBarView;
