import React, { useRef, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import KwaiImage from '@kds/image';
import KwaiPlayer from '@kds/player';
import { type DetailModel } from './DetailModels';

interface DetailCardProps {
  model: DetailModel;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#19191E',
    flex: 1,
    width: '100%',
  },
  desc: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 6,
    marginTop: 6,
    paddingHorizontal: 20,
    width: '100%',
  },
  descDivider: {
    backgroundColor: '#9c9c9c',
    height: 10,
    marginHorizontal: 8,
    width: 0.5,
  },
  descText: {
    color: '#909092',
    fontSize: 12,
    fontWeight: '400',
  },
  playButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    left: '50%',
    position: 'absolute',
    top: '50%',
    transform: [{ translateX: -20 }, { translateY: -20 }],
    width: 40,
  },
  playIcon: {
    color: '#000000',
    fontSize: 20,
  },
  previewArea: {
    width: '100%',
  },
  title: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '500',
    marginLeft: 20,
    marginTop: 24,
    width: '100%',
  },
  userAvatar: {
    borderRadius: 9,
    height: 18,
    width: 18,
  },
  userName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '400',
    marginLeft: 4,
    maxWidth: 100,
  },
  videoContainer: {
    overflow: 'hidden',
    position: 'relative',
    width: '100%',
  },
  videoPlayer: {
    height: '100%',
    width: '100%',
  },
});

const DetailCard: React.FC<DetailCardProps> = ({ model }) => {
  // 计算视频宽高比，限制在3/4到4/3之间
  const videoAspectRatio = (() => {
    if (model.videoWidth && model.videoHeight && model.videoWidth > 0 && model.videoHeight > 0) {
      const ratio = model.videoWidth / model.videoHeight;
      // 限制比例范围：最小3/4，最大4/3
      return Math.max(3 / 4, Math.min(4 / 3, ratio));
    }
    return 3 / 4; // 默认3:4比例
  })();

  // 动态视频容器样式
  const dynamicVideoContainerStyle = {
    ...styles.videoContainer,
    aspectRatio: videoAspectRatio,
  };

  // 播放器状态管理
  const player = useRef<any>(null);
  const [paused, setPaused] = useState(true); // 默认暂停

  // 播放/暂停切换
  const togglePlayPause = () => {
    setPaused(!paused);
  };

  return (
    <View style={styles.container}>
      {/* 预览区 */}
      <View style={styles.previewArea}>
        {/* 视频播放器区域 */}
        <View style={dynamicVideoContainerStyle}>
          {/* KwaiPlayer 视频播放器 */}
          <KwaiPlayer
            source={model.videoSource}
            ref={player}
            paused={paused}
            repeat={true}
            playInBackground={false}
            playWhenInactive={false}
            resizeMode="contain"
            muted={false}
            volume={1.0}
            style={styles.videoPlayer}
            poster={model.coverSource}
            posterResizeMode="cover"
            disableFocus={true}
            controls={false}
          />

          {/* 播放/暂停按钮 */}
          <TouchableOpacity style={styles.playButton} onPress={togglePlayPause}>
            <Text style={styles.playIcon}>{paused ? '▶' : '⏸'}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 标题区域 */}
      <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
        {model.materialName}
      </Text>

      {/* 描述信息 */}
      <View style={styles.desc}>
        <KwaiImage source={model.userAvatarSource} style={styles.userAvatar} resizeMode="cover" />
        <Text style={styles.userName} numberOfLines={1} ellipsizeMode="tail">
          {model.userName}
        </Text>
        <View style={styles.descDivider} />
        <Text style={styles.descText} numberOfLines={1} ellipsizeMode="tail">
          {model.usageDescription}
        </Text>
      </View>
    </View>
  );
};

export default DetailCard;
