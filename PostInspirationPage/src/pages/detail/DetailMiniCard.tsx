import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import KwaiImage from '@kds/image';
import { type DetailModel } from './DetailModels';

interface DetailMiniCardProps {
  model: DetailModel;
  onTitleLayout?: (layout: { y: number; height: number }) => void;
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'row',
    height: 104,
    width: '100%',
  },

  // 封面图 - magic类型
  coverImageMagic: {
    borderRadius: 18,
    height: 104,
    marginHorizontal: 12,
    width: 104,
  },

  // 封面图 - 非magic类型
  coverImageNormal: {
    borderRadius: 18,
    height: 90,
    marginHorizontal: 19,
    marginVertical: 7,
    width: 90,
  },

  // name文字样式
  name: {
    color: '#E6E6E6',
    fontSize: 20,
    fontWeight: '500',
  },

  // 右侧文字区域
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    marginRight: 19,
  },

  // usageInfo文字样式
  usageInfo: {
    color: '#B5B5B6',
    fontSize: 12,
    fontWeight: '400',
    marginTop: 8,
  },
});

const DetailMiniCard: React.FC<DetailMiniCardProps> = ({ model, onTitleLayout }) => {
  // 根据materialType选择封面图样式
  const coverImageStyle =
    model.materialType === 'magic' ? styles.coverImageMagic : styles.coverImageNormal;

  return (
    <View style={styles.container}>
      {/* 封面图 */}
      <KwaiImage source={model.coverSource} style={coverImageStyle} resizeMode="cover"  />

      {/* 右侧文字区域 */}
      <View style={styles.textContainer}>
        {/* 右侧名称区域 */}
        <Text
          style={styles.name}
          numberOfLines={2}
          ellipsizeMode="tail"
          onLayout={event => {
            const { y, height } = event.nativeEvent.layout;
            onTitleLayout?.({ y, height });
          }}
        >
          {model.materialName}
        </Text>
        <Text style={styles.usageInfo}>{model.usageDescription}</Text>
      </View>
    </View>
  );
};

export default memo(DetailMiniCard);
