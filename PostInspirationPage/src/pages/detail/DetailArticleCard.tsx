import React, { type VFC, memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import KwaiImage from '@kds/image';
import { type ArticleBean } from './DetailModels';
import { DetailUtils } from './DetailUtils';

interface DetailArticleCardProps {
  article: ArticleBean;
}

const styles = StyleSheet.create({
  // 文章内容容器
  articleContentContainer: {
    backgroundColor: '#222226',
    paddingBottom: 8,
    paddingHorizontal: 10,
    paddingTop: 10,
    width: '100%',
  },

  // 文章信息区域
  articleInfo: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 8,
    width: '100%',
  },

  // 文章文本
  articleText: {
    color: '#E6E6E6',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },

  // 头像图片
  avatarImage: {
    borderRadius: 12,
    height: 24,
    width: 24,
  },

  // 根容器
  container: {
    borderRadius: 2,
    marginVertical: 0,
    overflow: 'hidden',
    width: '100%',
  },

  // 封面图片
  coverImage: {
    aspectRatio: 9 / 16,
    width: '100%',
  },

  // 点赞容器
  likeContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flexShrink: 0,
    justifyContent: 'flex-end',
  },

  // 点赞图标
  likeIcon: {
    height: 18,
    width: 18,
  },

  // 点赞文本
  likeText: {
    color: '#B5B5B6',
    fontSize: 11,
    fontWeight: '400',
    lineHeight: 15,
  },

  // 发布时间
  postTime: {
    color: '#909092',
    fontSize: 9,
    fontWeight: '400',
    lineHeight: 13,
  },

  // 用户信息容器
  userInfoContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: 2,
    marginRight: 1,
    minWidth: 0,
  },

  // 用户名
  userName: {
    color: '#B5B5B6',
    fontSize: 11,
    fontWeight: '400',
    lineHeight: 15,
  },
});

const DetailArticleCard: VFC<DetailArticleCardProps> = ({ article }) => {
  return (
    <View style={styles.container}>
      {/* 封面图片组件 */}
      <KwaiImage source={article.coverSource} style={styles.coverImage} resizeMode="cover" />

      {/* 文章内容容器 */}
      <View style={styles.articleContentContainer}>
        {/* 文章文本 */}
        <Text style={styles.articleText} numberOfLines={2} ellipsizeMode="tail">
          {article.content}
        </Text>

        {/* 文章信息容器 */}
        <View style={styles.articleInfo}>
          {/* 用户头像 */}
          <KwaiImage
            source={article.userAvatarSource}
            style={styles.avatarImage}
            resizeMode="cover"
          />

          {/* 用户信息容器 */}
          <View style={styles.userInfoContainer}>
            {/* 用户名 */}
            <Text style={styles.userName} numberOfLines={1} ellipsizeMode="tail">
              {article.userName}
            </Text>
            {/* 发布时间 */}
            <Text style={styles.postTime}>{article.postTime}</Text>
          </View>

          {/* 点赞容器 */}
          <View style={styles.likeContainer}>
            {/* 点赞图标 */}
            <KwaiImage
              source={require('../../imgs/icon_like.png')}
              style={styles.likeIcon}
              resizeMode="contain"
            />
            {/* 点赞文本 */}
            <Text style={styles.likeText}>{DetailUtils.formatUsageCount(article.likeCount)}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(DetailArticleCard);
