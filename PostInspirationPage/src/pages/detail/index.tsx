import React, { type VFC, memo, useEffect } from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import { WrapContext } from '@kid-ui/krn';
import { type PageProps, usePage } from '@mfe/plus';
import { type PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailPage from './DetailPage';
import DetailPage2 from './DetailPage2';
import { DetailUtils } from './DetailUtils';

// 检测是否为 Web 平台
const isWeb = Platform.OS === 'web';

// 内容容器样式
const contentStyles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
});

const App: VFC<PageProps<Pages, PageList.Page2>> = ({ navigation }) => {
  usePage<Pages, PageList.Page2>();

  // 创建详情数据模型
  const detailModel = DetailUtils.parseAndCreateDetailModel();

  return (
    <View style={contentStyles.contentContainer}>
      {/* 根据 materialType 渲染不同的组件 */}
      {detailModel.materialType.toLowerCase() === 'template' ? (
        <DetailPage model={detailModel} navigation={navigation} />
      ) : (
        <DetailPage2 model={detailModel} navigation={navigation} />
      )}
    </View>
  );
};

export default WrapContext(memo<typeof App>(App), {
  styleConfig: {
    viewportWidthProvider: {
      getViewportWidth: ({ width }) => (isWeb ? 414 : Math.min(width, 414)),
    },
  },
});
