import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import { KidLoading } from '@kid-ui/krn';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailArticleCard from './DetailArticleCard';
import DetailArticleCardItem from './DetailArticleCardItem';
import { type ArticleBean, type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';
import { DetailUtils } from './DetailUtils';

interface ArticleListItem {
  h: number;
  bg: string;
  index: number;
  key: number;
  name: number;
  model: DetailModel;
  article: ArticleBean;
}

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

// 使用ArticleBean作为数据类型，通过ItemData包装

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#19191E',
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  itemContainer: {
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 4,
    width: '100%',
  },
  waterfallList: {
    flex: 1,
  },
});

let index = 0;

const getList = (length = 15): ArticleListItem[] => {
  return Array.from({ length }, () => {
    index++;
    return {
      h: Math.floor(Math.random() * 80) + 100,
      bg: 'xxxx',
      index: index,
      key: index,
      name: index,
      article: DetailUtils.createArticles()[0],
    };
  });
};


const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  // 滚动检测相关状态
  const actionAreaRef = useRef<View>(null);
  const [actionAreaLayout, setActionAreaLayout] = useState({ y: 0, height: 0 });
  const [isActionAreaVisible, setIsActionAreaVisible] = useState(true);

  // 底部操作区动画状态
  const bottomActionAreaTranslateY = useRef(new Animated.Value(89)).current;

  // 设置状态栏和导航栏颜色
  useEffect(() => {
    // 设置状态栏样式
    StatusBar.setBarStyle('dark-content', true);

    if (Platform.OS === 'android') {
      // Android: 设置状态栏背景色为黑色
      StatusBar.setBackgroundColor('#19191E', true);

      // Android: 设置底部导航栏为黑色
      try {
        // 尝试设置导航栏颜色，需要原生支持
        const { NativeModules } = require('react-native');
        if (NativeModules.NavigationBarColor) {
          NativeModules.NavigationBarColor.setNavigationBarColor('#19191E');
        }
      } catch (error) {
        console.log('设置Android导航栏颜色失败:', error);
      }
    } else if (Platform.OS === 'ios') {
      // iOS: 状态栏样式通过StatusBar组件和Info.plist配置
      // iOS没有底部导航栏，但可以设置状态栏为黑色内容
      StatusBar.setBarStyle('dark-content', true);

      // iOS: 如果需要隐藏状态栏，可以使用：
      // StatusBar.setHidden(false, 'fade');

      // iOS: 状态栏背景色需要通过原生代码或者在根视图设置背景色
      console.log('iOS状态栏设置为light-content');
    }

    // 组件卸载时恢复默认设置
    return () => {
      StatusBar.setBarStyle('default', true);
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('#FFFFFF', true);
      } else if (Platform.OS === 'ios') {
        StatusBar.setBarStyle('default', true);
      }
    };
  }, []);

  // 监听操作功能区可见性变化，控制底部操作区动画
  useEffect(() => {
    if (isActionAreaVisible) {
      // 操作区可见时，隐藏底部操作区（下降动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 89,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    } else {
      // 操作区不可见时，显示底部操作区（上推动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 0,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    }
  }, [isActionAreaVisible, bottomActionAreaTranslateY]);

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  // 处理操作功能区布局变化
  const handleActionAreaLayout = useCallback((event: any) => {
    const { y, height } = event.nativeEvent.layout;
    setActionAreaLayout({ y, height });
  }, []);

  const [list, changeList] = useState<ArticleListItem[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  const refresh = () => {
    // waterfallRef.current?.refreshList();
    // const articles = getArticles(20);
    // changeList(convertToWaterfallData(articles));

    console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
  };

  const onEndReached = () => {
    // changeList(prevList => {
    //   const articles = getArticles(20);
    //   const newItems = convertToWaterfallData(articles);
    //   const nList = [...prevList, ...newItems];
    //   console.log('test onEndReached', nList);
    //   return nList;
    // });
  };

  const onScroll = useCallback(() => {
    console.log('test onScroll');
  }, []);

  // 渲染组件函数
  const renderSeparator = useCallback(() => {
    return <View style={styles.separator}></View>;
  }, []);

  const renderHeader = useCallback(() => {
    return <DetailPageHeader model={model} />;
  }, [model]);

  const renderItem = useCallback(({ item }: { item: any }) => {
    // WaterFallList可能会包装数据，所以我们需要检查数据结构
    console.log('renderItem received item:', item);
    const articleItem = item as ArticleListItem;
    console.log('articleItem.article:', articleItem.article);
    return (
      <View style={styles.itemContainer}>
        <DetailArticleCard article={articleItem.article} />
      </View>
    );
  }, []);

  const renderEmpty = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>empty</Text>
      </View>
    );
  }, []);

  const renderFooter = useCallback(() => {
    return (
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>正在加载中</Text>
      </View>
    );
  }, []);

  const keyExtractor = useCallback((_item: any, key_index: number) => `row_${key_index}`, []);

  useEffect(() => {
    changeList(getList(20));
  }, []);

  return (
    <View style={styles.container}>
      {/* 状态栏设置 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === 'android' ? '#19191E' : undefined}
        translucent={false}
        hidden={false}
      />

      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      <WaterFallList
        keyExtractor={(_item, key_index) => `row_${key_index}`}
        ref={waterfallRef}
        ItemSeparatorComponent={() => {
          return <View style={styles.separator}></View>;
        }}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={() => {
          return <DetailPageHeader model={model} />;
        }}
        onScroll={onScroll}
        renderItem={({ item }) => <DetailArticleCardItem data={item} />}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => {
          return (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>empty</Text>
            </View>
          );
        }}
        ListFooterComponent={() => {
          return (
            <View style={styles.footerContainer}>
              <KidLoading useSvg type={'red'} size={48} />
            </View>
          );
        }}
      />
    </View>
  );
};

export default DetailPage;
