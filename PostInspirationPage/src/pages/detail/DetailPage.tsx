import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, Platform, StatusBar, StyleSheet, View } from 'react-native';
import { KidLoading } from '@kid-ui/krn';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailActionView from './DetailActionView';
import DetailArticleCardItem from './DetailArticleCardItem';
import { type ArticleBean, type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';
import { DetailUtils } from './DetailUtils';

interface ArticleListItem {
  key: number;
  model: DetailModel;
  article: ArticleBean;
}

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

const styles = StyleSheet.create({
  bottomActionContainer: {
    backgroundColor: 'transparent',
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
  },
  container: {
    backgroundColor: '#19191E',
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    height: '100%',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    height: 100,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  itemContainer: {
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 4,
    width: '100%',
  },
  waterfallContainer: {
    flex: 1,
  },
  waterfallList: {
    flex: 1,
  },
});

let index = 0;

const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  const getList = useCallback(
    (length = 10): ArticleListItem[] => {
      return Array.from({ length }, () => {
        index++;
        return {
          key: index,
          model,
          article: DetailUtils.createArticles()[0],
        };
      });
    },
    [model],
  );

  const [list, changeList] = useState<ArticleListItem[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  // 标题文字可见性状态
  const [titleTextVisible, setTitleTextVisible] = useState(false);

  // DetailCard 标题区域的布局信息
  const [cardTitleLayout, setCardTitleLayout] = useState({ y: 0, height: 0 });

  // 底部操作功能区状态管理
  const [bottomActionVisible, setBottomActionVisible] = useState(false);
  const bottomActionTranslateY = useRef(new Animated.Value(56)).current; // 初始状态：隐藏（向下偏移56px）

  // DetailPageHeader 中 DetailActionView 的布局信息
  const [headerActionLayout, setHeaderActionLayout] = useState({ y: 0, height: 0 });

  // 底部操作功能区动画控制
  const showBottomAction = useCallback(() => {
    console.log('showBottomAction: 开始显示底部操作功能区');
    setBottomActionVisible(true);
    Animated.timing(bottomActionTranslateY, {
      toValue: 0, // 上推到正常位置
      duration: 200,
      useNativeDriver: true,
      easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut 曲线
    }).start(() => {
      console.log('showBottomAction: 显示动画完成');
    });
  }, [bottomActionTranslateY]);

  const hideBottomAction = useCallback(() => {
    console.log('hideBottomAction: 开始隐藏底部操作功能区');
    setBottomActionVisible(false);
    Animated.timing(bottomActionTranslateY, {
      toValue: 56, // 下落隐藏
      duration: 200,
      useNativeDriver: true,
      easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut 曲线
    }).start(() => {
      console.log('hideBottomAction: 隐藏动画完成');
    });
  }, [bottomActionTranslateY]);

  const onEndReached = () => {
    // const nList = [...list, ...getList()];
    // console.log('test onEndReached', nList);
    // changeList(nList);
  };

  const onScroll = (event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;

    // 检测 DetailCard 标题区域是否可见
    // 标题区域的底部位置 = y + height
    const titleBottomY = cardTitleLayout.y + cardTitleLayout.height;

    // 当标题区域移出可见区域时（滚动位置超过标题底部），显示标题栏文字
    // 当标题区域移进可见区域时（滚动位置小于标题底部），隐藏标题栏文字
    const shouldShowTitleText = titleBottomY > 0 && scrollY > titleBottomY;

    // 检测 DetailPageHeader 中 DetailActionView 的可见性
    // DetailActionView 的底部位置 = y + height
    const headerActionBottomY = headerActionLayout.y + headerActionLayout.height;

    // 当 DetailPageHeader 中的 DetailActionView 可见时，底部操作功能区保持隐藏状态
    // 当 DetailPageHeader 中的 DetailActionView 不可见时，底部操作功能区显示出来
    const isHeaderActionVisible = headerActionLayout.y > 0 && scrollY < headerActionBottomY;
    const shouldShowBottomAction = headerActionLayout.y > 0 && !isHeaderActionVisible;

    console.log('test onScroll', {
      scrollY,
      titleBottomY,
      shouldShowTitleText,
      cardTitleLayout,
      headerActionBottomY,
      isHeaderActionVisible,
      shouldShowBottomAction,
      headerActionLayout,
      bottomActionVisible,
      waterfallPaddingBottom: bottomActionVisible ? 56 : 0,
    });

    // 只有当状态发生变化时才更新，避免频繁渲染
    if (shouldShowTitleText !== titleTextVisible) {
      setTitleTextVisible(shouldShowTitleText);
    }

    // 控制底部操作功能区的显示/隐藏
    if (shouldShowBottomAction && !bottomActionVisible) {
      showBottomAction();
    } else if (!shouldShowBottomAction && bottomActionVisible) {
      hideBottomAction();
    }
  };

  useEffect(() => {
    changeList(getList());
  }, [getList]);

  // 动态计算 WaterFallList 的内容容器样式
  const dynamicContentContainerStyle = [
    styles.contentContainer,
    { paddingBottom: bottomActionVisible ? 56 : 0 },
  ];

  return (
    <View style={styles.container}>
      {/* 状态栏设置 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === 'android' ? '#19191E' : undefined}
        translucent={false}
        hidden={false}
      />

      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        titleTextVisible={titleTextVisible}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      {/* 瀑布流容器 - 位于标题栏和底部操作功能区之间 */}
      <View style={styles.waterfallContainer}>
        <WaterFallList
          keyExtractor={(_item, key_index) => `row_${key_index}`}
          ref={waterfallRef}
          ItemSeparatorComponent={() => {
            return <View style={styles.separator}></View>;
          }}
          initialNumToRender={10}
          windowSize={10}
          ListHeaderComponent={useCallback(() => {
            return (
              <DetailPageHeader
                model={model}
                onTitleLayout={setCardTitleLayout}
                onActionLayout={setHeaderActionLayout}
              />
            );
          }, [model])}
          onScroll={onScroll}
          renderItem={({ item }) => <DetailArticleCardItem data={item} />}
          data={list}
          contentContainerStyle={dynamicContentContainerStyle}
          onEndReachedThreshold={0.5}
          onEndReached={onEndReached}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={() => {
            return <View style={styles.emptyContainer} />;
          }}
          ListFooterComponent={() => {
            return (
              <View style={styles.footerContainer}>
                <KidLoading useSvg type={'red'} size={40} />
              </View>
            );
          }}
        />
      </View>

      {/* 底部操作功能区 - 动画控制 */}
      <Animated.View
        style={[
          styles.bottomActionContainer,
          {
            transform: [{ translateY: bottomActionTranslateY }],
          },
        ]}
        pointerEvents={bottomActionVisible ? 'auto' : 'none'}
      >
        <DetailActionView model={model} isSplitEqually={true} />
      </Animated.View>
    </View>
  );
};

export default DetailPage;
