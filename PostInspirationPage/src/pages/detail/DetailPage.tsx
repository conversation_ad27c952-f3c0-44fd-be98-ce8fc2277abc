import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import { KidLoading, WrapContext } from '@kid-ui/krn';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailArticleCardItem from './DetailArticleCardItem';
import { type ArticleBean, type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';
import { DetailUtils } from './DetailUtils';

interface ArticleListItem {
  key: number;
  model: DetailModel;
  article: ArticleBean;
}

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

// 使用ArticleBean作为数据类型，通过ItemData包装

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#19191E',
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    height: '100%',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  itemContainer: {
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 4,
    width: '100%',
  },
  waterfallList: {
    flex: 1,
  },
});

const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  // 设置状态栏和导航栏颜色
  useEffect(() => {
    // 设置状态栏样式
    StatusBar.setBarStyle('dark-content', true);

    if (Platform.OS === 'android') {
      // Android: 设置状态栏背景色为黑色
      StatusBar.setBackgroundColor('#19191E', true);

      // Android: 设置底部导航栏为黑色
      try {
        // 尝试设置导航栏颜色，需要原生支持
        const { NativeModules } = require('react-native');
        if (NativeModules.NavigationBarColor) {
          NativeModules.NavigationBarColor.setNavigationBarColor('#19191E');
        }
      } catch (error) {
        console.log('设置Android导航栏颜色失败:', error);
      }
    } else if (Platform.OS === 'ios') {
      // iOS: 状态栏样式通过StatusBar组件和Info.plist配置
      // iOS没有底部导航栏，但可以设置状态栏为黑色内容
      StatusBar.setBarStyle('dark-content', true);

      // iOS: 如果需要隐藏状态栏，可以使用：
      // StatusBar.setHidden(false, 'fade');

      // iOS: 状态栏背景色需要通过原生代码或者在根视图设置背景色
      console.log('iOS状态栏设置为light-content');
    }

    // 组件卸载时恢复默认设置
    return () => {
      StatusBar.setBarStyle('default', true);
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('#FFFFFF', true);
      } else if (Platform.OS === 'ios') {
        StatusBar.setBarStyle('default', true);
      }
    };
  }, []);

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  let index = 0;

  const getList = (length = 15): ArticleListItem[] => {
    return Array.from({ length }, () => {
      index++;
      return {
        key: index,
        model,
        article: DetailUtils.createArticles()[0],
      };
    });
  };

  const [list, changeList] = useState([]);
  const waterfallRef = useRef<IWaterFallList>();

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const refresh = () => {
    index = 0;
    waterfallRef.current?.refreshList();
    changeList(getList());
    console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
  };

  const onEndReached = () => {
    const nList = [...list, ...getList(20)];
    console.log('test onEndReached', nList);
    changeList(nList);
  };

  const onScroll = () => {
    console.log('test onScroll');
  };

  useEffect(() => {
    changeList(getList());
  }, []);

  return (
    <View style={styles.container}>
      {/* 状态栏设置 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === 'android' ? '#19191E' : undefined}
        translucent={false}
        hidden={false}
      />

      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      <WaterFallList
        keyExtractor={(_item, key_index) => `row_${key_index}`}
        ref={waterfallRef}
        ItemSeparatorComponent={() => {
          return <View style={styles.separator}></View>;
        }}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={() => {
          return <DetailPageHeader model={model} />;
        }}
        onScroll={onScroll}
        renderItem={({ item }) => <DetailArticleCardItem data={item} />}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => {
          return <View style={styles.emptyContainer} />;
        }}
        ListFooterComponent={() => {
          return (
            <View style={styles.footerContainer}>
              <KidLoading useSvg type={'red'} size={40} />
            </View>
          );
        }}
      />
    </View>
  );
};

export default WrapContext(DetailPage);
