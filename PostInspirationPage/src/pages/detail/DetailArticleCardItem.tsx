import React, { memo } from 'react';
import { StyleSheet, View } from 'react-native';
import { type ItemData } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import DetailArticleCard from './DetailArticleCard';

interface DetailArticleCardItemProps {
  data: ItemData;
}

const styles = StyleSheet.create({
  firstColumn: {
    marginLeft: 4,
    marginRight: 2,
  },

  secondColumn: {
    marginLeft: 2,
    marginRight: 4,
  },
});

const DetailArticleCardItem = ({ data }: DetailArticleCardItemProps) => {
  const style = data.columnIndex === 0 ? styles.firstColumn : styles.secondColumn;

  return (
    <View style={style}>
      <DetailArticleCard article={data.itemData.article} model={data.itemData.model} />
    </View>
  );
};

export default memo<typeof DetailArticleCardItem>(DetailArticleCardItem);
