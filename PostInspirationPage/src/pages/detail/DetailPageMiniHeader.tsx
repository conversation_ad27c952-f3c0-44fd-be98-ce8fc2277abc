import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import DetailMiniCard from './DetailMiniCard';
import { type DetailModel } from './DetailModels';

interface DetailPageHeaderProps {
  model: DetailModel;
  onTitleLayout?: (layout: { y: number; height: number }) => void;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#19191E',
  },
  relatedArticleText: {
    color: '#B5B5B6',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
    marginStart: 19,
    marginTop: 24,
  },
});

const DetailPageHeader = ({ model, onTitleLayout }: DetailPageHeaderProps) => {
  return (
    <View style={styles.container}>
      {/* 详情卡片 */}
      <DetailMiniCard model={model} onTitleLayout={onTitleLayout} />

      {/* 相关作品标题 */}
      <Text style={styles.relatedArticleText}>相关作品</Text>
    </View>
  );
};

// 使用 memo 包装组件，避免不必要的重新渲染
export default memo(DetailPageHeader);
