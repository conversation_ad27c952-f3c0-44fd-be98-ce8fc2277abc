import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import DetailActionView from './DetailActionView';
import DetailCard from './DetailCard';
import { type DetailModel } from './DetailModels';

interface DetailPageHeaderProps {
  model: DetailModel;
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#19191E',
  },
  divider: {
    backgroundColor: '#FFFFFF0D',
    height: 0.5,
    marginHorizontal: 19,
    marginVertical: 16,
  },
  relatedArticleText: {
    color: '#B5B5B6',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 16,
    marginStart: 19,
  },
});

const DetailPageHeader = ({ model }: DetailPageHeaderProps) => {
  return (
    <View style={styles.container}>
      {/* 详情卡片 */}
      <DetailCard model={model} />

      {/* 操作功能区 */}
      <DetailActionView model={model} isSplitEqually={false} />

      {/* 分割线 */}
      <View style={styles.divider} />

      {/* 相关作品标题 */}
      <Text style={styles.relatedArticleText}>相关作品</Text>
    </View>
  );
};

// 使用 memo 包装组件，避免不必要的重新渲染
export default memo(DetailPageHeader);
