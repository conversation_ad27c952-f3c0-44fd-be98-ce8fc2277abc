import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Platform, StatusBar, StyleSheet, View } from 'react-native';
import { KidLoading } from '@kid-ui/krn';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailActionView from './DetailActionView';
import DetailArticleCardItem from './DetailArticleCardItem';
import { type ArticleBean, type DetailModel } from './DetailModels';
import DetailPageMiniHeader from './DetailPageMiniHeader';
import DetailTitleBarView from './DetailTitleBarView';
import { DetailUtils } from './DetailUtils';

interface ArticleListItem {
  key: number;
  model: DetailModel;
  article: ArticleBean;
}

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

const styles = StyleSheet.create({
  bottomActionContainer: {
    backgroundColor: 'transparent',
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
  },
  container: {
    backgroundColor: '#19191E',
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    height: '100%',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    height: 100,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  itemContainer: {
    width: '100%',
  },
  separator: {
    height: 4,
    width: '100%',
  },
  waterfallContainer: {
    flex: 1,
  },
  waterfallList: {
    flex: 1,
  },
});

let index = 0;

const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  const getList = useCallback(
    (length = 10): ArticleListItem[] => {
      return Array.from({ length }, () => {
        index++;
        return {
          key: index,
          model,
          article: DetailUtils.createArticles()[0],
        };
      });
    },
    [model],
  );

  const [list, changeList] = useState<ArticleListItem[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  // 标题文字可见性状态
  const [titleTextVisible, setTitleTextVisible] = useState(false);

  // DetailCard 标题区域的布局信息
  const [cardTitleLayout, setCardTitleLayout] = useState({ y: 0, height: 0 });

  const onEndReached = () => {
    // const nList = [...list, ...getList()];
    // console.log('test onEndReached', nList);
    // changeList(nList);
  };

  const onScroll = (event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;

    // 检测 DetailMiniCard 名称区域是否可见
    // 名称区域的底部位置 = y + height
    const nameBottomY = cardTitleLayout.y + cardTitleLayout.height;

    // 当名称区域移出可见区域时（滚动位置超过名称底部），显示标题栏文字
    // 当名称区域移进可见区域时（滚动位置小于名称底部），隐藏标题栏文字
    const shouldShowTitleText = nameBottomY > 0 && scrollY > nameBottomY;

    console.log('DetailPage2 onScroll', {
      scrollY,
      nameBottomY,
      shouldShowTitleText,
      cardTitleLayout,
    });

    // 只有当状态发生变化时才更新，避免频繁渲染
    if (shouldShowTitleText !== titleTextVisible) {
      setTitleTextVisible(shouldShowTitleText);
    }
  };

  useEffect(() => {
    changeList(getList());
  }, [getList]);

  return (
    <View style={styles.container}>
      {/* 状态栏设置 */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Platform.OS === 'android' ? '#19191E' : undefined}
        translucent={false}
        hidden={false}
      />

      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        titleTextVisible={titleTextVisible}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      {/* 瀑布流容器 - 位于标题栏和底部操作功能区之间 */}
      <View style={styles.waterfallContainer}>
        <WaterFallList
          keyExtractor={(_item, key_index) => `row_${key_index}`}
          ref={waterfallRef}
          ItemSeparatorComponent={() => {
            return <View style={styles.separator}></View>;
          }}
          initialNumToRender={10}
          windowSize={10}
          ListHeaderComponent={useCallback(() => {
            return <DetailPageMiniHeader model={model} onTitleLayout={setCardTitleLayout} />;
          }, [model])}
          onScroll={onScroll}
          renderItem={({ item }) => <DetailArticleCardItem data={item} />}
          data={list}
          contentContainerStyle={styles.contentContainer}
          onEndReachedThreshold={0.5}
          onEndReached={onEndReached}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={() => {
            return <View style={styles.emptyContainer} />;
          }}
          ListFooterComponent={() => {
            return (
              <View style={styles.footerContainer}>
                <KidLoading useSvg type={'red'} size={40} />
              </View>
            );
          }}
        />
      </View>

      {/* 底部操作功能区 */}
      <DetailActionView model={model} isSplitEqually={true} />
    </View>
  );
};

export default DetailPage;
