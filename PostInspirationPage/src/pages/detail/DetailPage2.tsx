import React, { useCallback } from 'react';
import { ScrollView, StyleSheet, Text, View } from 'react-native';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailActionView from './DetailActionView';
import DetailMiniCard from './DetailMiniCard';
import { type DetailModel } from './DetailModels';
import DetailTitleBarView from './DetailTitleBarView';

interface DetailPage2Props {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    flex: 1,
  },

  detailCardArea: {
    paddingTop: 0,
  },

  relatedArticleText: {
    color: '#222222',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    marginBottom: 16,
    marginStart: 19,
    marginTop: 24,
  },

  scrollContent: {
    flexGrow: 1,
  },

  waterfallContainer: {
    marginHorizontal: 4,
  },
});

const DetailPage2: React.FC<DetailPage2Props> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage2', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage2', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  return (
    <View style={styles.container}>
      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      {/* 滑动区域 */}
      <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* 详情卡片 */}
        <View style={styles.detailCardArea}>
          <DetailMiniCard model={model} />
        </View>

        {/* 相关作品标题 */}
        <Text style={styles.relatedArticleText}>相关作品</Text>
      </ScrollView>

      {/* 底部功能区 */}
      <DetailActionView model={model} isSplitEqually={false} />
    </View>
  );
};

export default DetailPage2;
